import useTabStore, { useTabId, useTabType } from '@/stores/tab.store';
import { MessageCircleMore } from 'lucide-react';
import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useKeyDown } from 'services/Hooks';
import { AxonButton } from 'ui/UIComponents';
import { createTabSessionKey, useChatbotStore, useCurrentTabSession } from './chatbotStore';
import Chatbox from './Chatbox';
import SendBotMessage from './Chatbox/SendBotMessage';

const Chatbot = () => {
  const { t } = useTranslation();

  // const { sendJsonMessage } = useChatbotWs();

  // Get current tab information
  const tabId = useTabId();
  const tabType = useTabType();
  const tabSessions = useChatbotStore.use.tabSessions();
  const currentTabSession = useCurrentTabSession();
  const updateCurrentTabSession = useChatbotStore.use.updateCurrentTabSession();

  // Chatbot store actions
  const initTabSession = useChatbotStore.use.initTabSession();
  const setCurrentTab = useChatbotStore.use.setCurrentTab();
  const removeTabSession = useChatbotStore.use.removeTabSession();

  // Track all tabs to detect when they're removed
  const allTabs = useTabStore((state) => state.allTabs);
  const previousTabsRef = useRef<typeof allTabs>([]);

  const toggle = () => {
    if (!tabId || !tabType) return;
    updateCurrentTabSession({
      isOpen: !currentTabSession?.isOpen,
    });
  };

  useKeyDown([
    [
      'ctrl+d',
      (e) => {
        e.preventDefault();
        toggle();
      },
    ],
    [
      'command+d',
      (e) => {
        e.preventDefault();
        toggle();
      },
    ],
  ]);

  // Initialize and manage tab sessions
  useEffect(() => {
    if (!tabId || !tabType) return;

    const tabKey = createTabSessionKey(tabId, tabType);

    // Initialize session for this tab
    initTabSession(tabKey);

    // Set this tab as current tab
    setCurrentTab(tabKey);
  }, [tabId, tabType, initTabSession, setCurrentTab]);

  // Clean up chatbot sessions when tabs are removed
  useEffect(() => {
    const previousTabs = previousTabsRef.current;
    const currentTabs = allTabs;

    // Find removed tabs
    const removedTabs = previousTabs.filter(
      (prevTab) => !currentTabs.some((currTab) => currTab.id === prevTab.id && currTab.type === prevTab.type),
    );

    // Remove chatbot sessions for removed tabs
    removedTabs.forEach((tab) => {
      const tabKey = createTabSessionKey(tab.id, tab.type);
      removeTabSession(tabKey);
    });

    // Update the ref
    previousTabsRef.current = currentTabs;
  }, [allTabs, removeTabSession]);

  // Don't render chatbot if no active tab
  if (!tabId || !tabType) {
    return null;
  }

  return (
    <div className='fixed bottom-6 right-6 z-10'>
      {!currentTabSession?.isOpen && (
        <AxonButton variant='primary' onClick={toggle} startDecorator={<MessageCircleMore className='size-4' />}>
          {t('ai_chat')}
          <span className='border-gradient-border rounded-sm border bg-white/25 px-[4px] text-xs'>(⌘ D)</span>
        </AxonButton>
      )}
      {currentTabSession?.isOpen && <Chatbox toggle={toggle} />}

      {/* Maintain one WebSocket per tab session so switching tabs doesn't close others */}
      {Object.entries(tabSessions).map(([key, session]) => {
        if (session.chatbotUrl) {
          console.log(`[Chatbot] Rendering SendBotMessage for ${key}`, { url: session.chatbotUrl });
          return <SendBotMessage key={key} tabKey={key} url={session.chatbotUrl} />;
        }
        return null;
      })}
    </div>
  );
};

export default Chatbot;
