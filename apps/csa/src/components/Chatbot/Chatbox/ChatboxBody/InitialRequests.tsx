import { useTabLineId } from '@/stores/tab.store';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ReadyState } from 'react-use-websocket';
import { AxonButton } from 'ui/UIComponents';
import { useCurrentTabWebSocketState } from '../../hooks/useChatbotWs';
import { useSendUserMessageToWS } from '../../hooks/useSendUserMessageToWS';

type InitialRequest = {
  id: string;
  text: string;
  shortcut: string;
};

type InitialRequestButtonProps = {
  request: InitialRequest;
} & React.ComponentProps<typeof AxonButton>;

const InitialRequestButton = ({ request, ...rest }: InitialRequestButtonProps) => {
  return (
    <AxonButton className='text-md justify-between gap-1 font-normal' {...rest}>
      <p>{request.text}</p>
      <p>{`(⌘${request.shortcut})`.toUpperCase()}</p>
    </AxonButton>
  );
};

const InitialRequests = () => {
  const { t } = useTranslation();

  const lineId = useTabLineId() || '';

  const { readyState } = useCurrentTabWebSocketState();

  const sendUserMessageToWS = useSendUserMessageToWS();

  const initialRequests = useMemo(
    () => [
      {
        id: 'ask-question',
        text: t('ask_questions_to_ai'),
        shortcut: 'a',
      },
      {
        id: 'take-note',
        text: t('take_note_on_this_customer'),
        shortcut: 'd',
      },
      {
        id: 'create-new-case',
        text: t('create_new_case_and_escalate'),
        shortcut: 'e',
      },
    ],
    [t],
  );

  const selectInitialRequest = useCallback(
    (request: (typeof initialRequests)[number]) => {
      if (readyState !== ReadyState.OPEN) return;
      if (lineId && request.id === 'take-note') {
        sendUserMessageToWS(`${request.text} ${lineId}`);
      } else {
        sendUserMessageToWS(request.text);
      }
    },
    [sendUserMessageToWS, readyState, lineId],
  );

  useEffect(() => {
    const handleKeydown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && ['a', 'd', 'e'].includes(e.key)) {
        e.preventDefault();
        const selectedRequest = initialRequests.find((request) => request.shortcut === e.key);
        if (selectedRequest) selectInitialRequest(selectedRequest);
      }
    };
    document.addEventListener('keydown', handleKeydown);
    return () => document.removeEventListener('keydown', handleKeydown);
  }, [initialRequests, selectInitialRequest]);

  return (
    <div className='flex w-10/12 flex-col gap-2'>
      {initialRequests.map((request) => (
        <InitialRequestButton request={request} key={request.id} onClick={() => selectInitialRequest(request)} />
      ))}
    </div>
  );
};
export default InitialRequests;
