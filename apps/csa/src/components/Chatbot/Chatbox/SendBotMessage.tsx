import { useCallback, useEffect, useRef } from 'react';
import { useChatbotStore } from '../chatbotStore';
import { useChatbotWsByUrl } from '../hooks/useChatbotWs';
import { TabSessionKey } from '../types';
import { getWsMessage } from '../utils';
type Props = { tabKey: TabSessionKey; url: string };

const SendBotMessage = ({ tabKey, url }: Props) => {
  console.log(`[SendBotMessage] Component rendered for ${tabKey}`, { url });

  const { lastJsonMessage, sendJsonMessage } = useChatbotWsByUrl(url);
  const sendChatbotMessageForTab = useChatbotStore.use.sendMessageForTab();
  const updateChatbotMessageForTab = useChatbotStore.use.updateMessageForTab();
  const removeLoadingMessageForTab = useChatbotStore.use.removeLoadingMessageForTab();
  const modifyWaitingStateForTab = useChatbotStore.use.modifyWaitingStateForTab();
  const markTabAsInitialized = useChatbotStore.use.markTabAsInitialized();
  const tabSessions = useChatbotStore.use.tabSessions();
  const currentTabKey = useChatbotStore.use.currentTabKey();

  const botMessageIdRef = useRef<string | null>(null);
  const characterQueueRef = useRef<string[]>([]);
  const displayedContentRef = useRef<string>(''); // This contains the current content of the message and is updated in setInterval
  const typingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const initializedMessageIdsRef = useRef<Set<string>>(new Set());

  const TYPING_SPEED = 10;
  const CHARACTER_PER_INTERVAL = 5;

  const startTypingLoop = useCallback(() => {
    modifyWaitingStateForTab(tabKey, true);
    // this condition prevents multiple setInterval timers from running at the same time
    // because each new chunk will trigger this function
    if (typingIntervalRef.current) return;

    typingIntervalRef.current = setInterval(() => {
      if (characterQueueRef.current.length === 0) return;

      const nextChars = characterQueueRef.current.splice(0, CHARACTER_PER_INTERVAL).join('');
      if (!nextChars) return;

      displayedContentRef.current += nextChars;

      if (botMessageIdRef.current) {
        updateChatbotMessageForTab(tabKey, {
          id: botMessageIdRef.current,
          content: displayedContentRef.current,
        });
      }
    }, TYPING_SPEED);
  }, [modifyWaitingStateForTab, updateChatbotMessageForTab, tabKey]);

  const stopTypingLoop = useCallback(() => {
    if (typingIntervalRef.current) {
      clearInterval(typingIntervalRef.current);
      typingIntervalRef.current = null;
      modifyWaitingStateForTab(tabKey, false);
    }
  }, [modifyWaitingStateForTab, tabKey]);

  useEffect(() => {
    if (!lastJsonMessage || lastJsonMessage.response_type === 'system') return;

    const message = lastJsonMessage;

    if (message.delta) {
      // First delta -> init message
      if (!botMessageIdRef.current) {
        if (!message.message_id || !initializedMessageIdsRef.current.has(message.message_id)) {
          if (message.message_id) initializedMessageIdsRef.current.add(message.message_id);

          removeLoadingMessageForTab(tabKey);

          sendChatbotMessageForTab(tabKey, {
            id: message.message_id,
            owner: 'bot',
            content: '', // Send empty message initially
          });

          botMessageIdRef.current = message.message_id;
          displayedContentRef.current = '';
        } else {
          // Already initialized earlier (while tab hidden/remounted). Just resume typing into that message id
          botMessageIdRef.current = message.message_id;
        }
      }

      // Add characters to queue
      characterQueueRef.current.push(...message.delta.split(''));
      startTypingLoop();
    }

    if (message.status === 'complete') {
      const finishTyping = () => {
        // We need to wait for the queue to empty
        // The reason is that the queue runs slower than retrieving responses from server
        if (characterQueueRef.current.length > 0) {
          setTimeout(finishTyping, TYPING_SPEED);
          return;
        }

        // Now the queue is empty
        // Final update to ensure content is accurate
        if (botMessageIdRef.current) {
          updateChatbotMessageForTab(tabKey, {
            id: botMessageIdRef.current,
            content: displayedContentRef.current,
          });
        }

        // Reset everything
        stopTypingLoop();
        botMessageIdRef.current = null;
        characterQueueRef.current = [];
        displayedContentRef.current = '';
      };

      finishTyping();
    }
  }, [
    lastJsonMessage,
    sendChatbotMessageForTab,
    updateChatbotMessageForTab,
    removeLoadingMessageForTab,
    startTypingLoop,
    stopTypingLoop,
    modifyWaitingStateForTab,
    tabKey,
  ]);

  // Handle initialization for customer tabs
  useEffect(() => {
    // Parse tabKey to get tabId and tabType
    const [tabId, tabType] = tabKey.split('-');
    const currentSession = tabSessions[tabKey];

    console.log(`[SendBotMessage] Initialization useEffect running for ${tabKey}`, {
      tabType,
      isCurrentTab: tabKey === currentTabKey,
      hasInitializedCustomer: currentSession?.hasInitializedCustomer,
      messagesLength: currentSession?.messages.length,
      hasSendJsonMessage: !!sendJsonMessage,
    });

    // Only proceed if:
    // 1. It's a customer tab
    // 2. It's the current active tab
    // 3. We haven't initialized yet
    if (tabType !== 'customer' || tabKey !== currentTabKey || currentSession?.hasInitializedCustomer) {
      console.log(`[SendBotMessage] Skipping initialization for ${tabKey}`, {
        tabType,
        isCurrentTab: tabKey === currentTabKey,
        hasInitializedCustomer: currentSession?.hasInitializedCustomer,
      });
      return;
    }

    // Send initialization message when:
    // 1. The session exists and has no messages
    // 2. We have a sendJsonMessage function (WebSocket is connected)
    if (currentSession && currentSession.messages.length === 0 && sendJsonMessage) {
      console.log(`[SendBotMessage] Sending line_id initialization for ${tabKey}`, {
        messagesLength: currentSession.messages.length,
        hasConnection: !!sendJsonMessage,
      });
      markTabAsInitialized(tabKey);
      sendJsonMessage(getWsMessage(`line_id ${tabId} - new analysis`));
    } else {
      console.log(`[SendBotMessage] Not sending initialization for ${tabKey}`, {
        hasSession: !!currentSession,
        messagesLength: currentSession?.messages.length,
        hasConnection: !!sendJsonMessage,
      });
    }
  }, [tabKey, currentTabKey, tabSessions, sendJsonMessage, markTabAsInitialized]);

  return null;
};

export default SendBotMessage;
