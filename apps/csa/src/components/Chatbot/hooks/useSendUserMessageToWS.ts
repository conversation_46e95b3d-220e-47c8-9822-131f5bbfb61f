import { useChatbotStore, useCurrentTabChatbotUrl } from '../chatbotStore';
import { getWsMessage } from '../utils';
import { useChatbotWsByUrl } from './useChatbotWs';

export const useSendUserMessageToWS = () => {
  const chatbotUrl = useCurrentTabChatbotUrl();
  // Use the same WebSocket instance that SendBotMessage creates for the current tab
  const { sendJsonMessage } = useChatbotWsByUrl(chatbotUrl, {}, true); // shouldConnect = false, just get the existing connection

  const sendChatbotMessage = useChatbotStore.use.sendMessage();
  const sendLoadingMessage = useChatbotStore.use.sendLoadingMessage();
  return (content: string) => {
    sendChatbotMessage({ owner: 'user', content });
    sendLoadingMessage();
    if (sendJsonMessage) {
      sendJsonMessage(getWsMessage(content));
    }
  };
};
