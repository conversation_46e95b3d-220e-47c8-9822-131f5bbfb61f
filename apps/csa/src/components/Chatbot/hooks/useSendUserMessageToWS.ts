import { useChatbotStore, useCurrentTabChatbotUrl } from '../chatbotStore';
import { getWsMessage } from '../utils';
import { useChatbotWsByUrl } from './useChatbotWs';

export const useSendUserMessageToWS = () => {
  const chatbotUrl = useCurrentTabChatbotUrl();
  // Use the same WebSocket instance that SendBotMessage creates for the current tab
  const { sendJsonMessage, readyState } = useChatbotWsByUrl(chatbotUrl, {}, true); // shouldConnect = true to ensure connection exists

  const sendChatbotMessage = useChatbotStore.use.sendMessage();
  const sendLoadingMessage = useChatbotStore.use.sendLoadingMessage();

  return (content: string) => {
    console.log('[useSendUserMessageToWS] Attempting to send message:', {
      content,
      chatbotUrl,
      hasSendJsonMessage: !!sendJsonMessage,
      readyState,
      readyStateText:
        readyState === 0
          ? 'CONNECTING'
          : readyState === 1
            ? 'OPEN'
            : readyState === 2
              ? 'CLOSING'
              : readyState === 3
                ? 'CLOSED'
                : 'UNKNOWN',
    });

    sendChatbotMessage({ owner: 'user', content });
    sendLoadingMessage();

    if (sendJsonMessage) {
      console.log('[useSendUserMessageToWS] Sending message to WebSocket');
      sendJsonMessage(getWsMessage(content));
    } else {
      console.log('[useSendUserMessageToWS] Cannot send - sendJsonMessage is null');
    }
  };
};
