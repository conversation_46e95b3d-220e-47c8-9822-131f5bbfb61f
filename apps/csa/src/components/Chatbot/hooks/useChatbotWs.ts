import useWebSocket, { Options } from 'react-use-websocket';
import { useCurrentTabChatbotUrl } from '../chatbotStore';
import { BotMessageFromWs } from '../types';

export const useChatbotWs = (options?: Options, shouldConnect: boolean = true) => {
  const chatbotUrl = useCurrentTabChatbotUrl();
  return useWebSocket<BotMessageFromWs>(
    chatbotUrl,
    {
      share: true, // Share a single socket across components for the same URL
      ...options,
    },
    shouldConnect && !!chatbotUrl, // Only connect if we have a URL
  );
};

// URL-based variant for persistent per-session connections
export const useChatbotWsByUrl = (url: string | null | undefined, options?: Options, shouldConnect: boolean = true) => {
  return useWebSocket<BotMessageFromWs>(
    url ?? null,
    {
      share: true, // still share among components bound to the same session URL
      ...options,
    },
    shouldConnect && !!url,
  );
};
