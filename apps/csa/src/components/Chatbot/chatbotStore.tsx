import { v4 as uuidv4 } from 'uuid';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { useShallow } from 'zustand/react/shallow';
import {
  ChatbotTabSession,
  ChatbotTabSessions,
  ChatMessageEntity,
  ChatMessageToSend,
  createSelectors,
  TabSessionKey,
} from './types';

type ChatbotStore = {
  tabSessions: ChatbotTabSessions;
  currentTabKey: TabSessionKey | null;

  // Tab session management
  initTabSession: (tabKey: TabSessionKey) => void;
  setCurrentTab: (tabKey: TabSessionKey) => void;
  removeTabSession: (tabKey: TabSessionKey) => void;
  updateCurrentTabSession: (payload: Partial<ChatbotTabSession>) => void;

  sendMessage: (message: ChatMessageToSend) => void;
  updateMessage: (params: { id: string; content: string }) => void;
  startChatMessage: () => void;
  sendLoadingMessage: () => void;
  removeLoadingMessage: () => void;
  modifyWaitingState: (isWaiting: boolean) => void;

  // Per-tab scoped actions
  sendMessageForTab: (tabKey: TabSessionKey, message: ChatMessageToSend) => void;
  updateMessageForTab: (tabKey: TabSessionKey, params: { id: string; content: string }) => void;
  sendLoadingMessageForTab: (tabKey: TabSessionKey) => void;
  removeLoadingMessageForTab: (tabKey: TabSessionKey) => void;
  modifyWaitingStateForTab: (tabKey: TabSessionKey, isWaiting: boolean) => void;
  markTabAsInitialized: (tabKey: TabSessionKey) => void;
  initializeCustomerTabIfNeeded: (tabKey: TabSessionKey) => boolean;

  // Getters for current tab
  getCurrentTabSession: () => ChatbotTabSession | null;
  getMessages: () => ChatMessageEntity[];
  getMessageHasStarted: () => boolean;
  getIsWaiting: () => boolean;
  getChatbotUrl: () => string;
};

export const createTabSessionKey = (tabId: string, tabType: 'customer' | 'device'): TabSessionKey =>
  `${tabId}-${tabType}`;

const createDefaultTabSession = () => ({
  messages: [],
  messageHasStarted: false,
  isWaiting: false,
  chatbotUrl: '',
  isInitialized: false,
  isOpen: false,
  hasInitializedCustomer: false, // Track if we've sent the initial customer message
});

const createChatbotUrl = (): string => {
  const clientId = uuidv4();
  const baseUrl = process.env.CHATBOT_WS_URL ?? `ws://vedge-staging.axon-networks.com/neura-agent/agent`;
  return `${baseUrl}/${clientId}`;
};

// Each tab creates a new WS session
export const useChatbotStoreBase = create<ChatbotStore>()(
  immer(
    devtools(
      (set, get) => ({
        tabSessions: {},
        currentTabKey: null,
        initTabSession: (tabKey: TabSessionKey) => {
          set((state) => {
            if (!state.tabSessions[tabKey]) {
              const session = createDefaultTabSession();
              session.chatbotUrl = createChatbotUrl();
              state.tabSessions[tabKey] = session;
            }
          });
        },
        setCurrentTab: (tabKey: TabSessionKey) => {
          set((state) => {
            state.currentTabKey = tabKey;
          });
        },
        getCurrentTabSession: () => {
          const state = get();
          return state.currentTabKey ? state.tabSessions[state.currentTabKey] || null : null;
        },
        getMessages: () => {
          const session = get().getCurrentTabSession();
          return session?.messages || [];
        },

        getMessageHasStarted: () => {
          const session = get().getCurrentTabSession();
          return session?.messageHasStarted || false;
        },

        getIsWaiting: () => {
          const session = get().getCurrentTabSession();
          return session?.isWaiting || false;
        },

        getChatbotUrl: () => {
          const session = get().getCurrentTabSession();
          return session?.chatbotUrl || '';
        },
        removeTabSession: (tabKey: TabSessionKey) => {
          set((state) => {
            delete state.tabSessions[tabKey];
            if (state.currentTabKey === tabKey) {
              state.currentTabKey = null;
            }
          });
        },
        updateCurrentTabSession: (payload: Partial<ChatbotTabSession>) => {
          set((state) => {
            if (!state.currentTabKey) return;
            state.tabSessions[state.currentTabKey] = {
              ...state.tabSessions[state.currentTabKey],
              ...payload,
            };
          });
        },
        startChatMessage: () => {
          set((state) => {
            const session = state.currentTabKey ? state.tabSessions[state.currentTabKey] : null;
            if (session && !session.messageHasStarted) {
              session.messageHasStarted = true;
            }
          });
        },
        sendMessage: (message: ChatMessageToSend) => {
          set((state) => {
            const session = state.currentTabKey ? state.tabSessions[state.currentTabKey] : null;
            if (session) {
              const newMessage = {
                ...message,
                id: message.id || uuidv4(),
                createdAt: message['createdAt'] || new Date().toISOString(),
              };
              session.messages.push(newMessage);
            }
          });
        },
        updateMessage: ({ id, content }) => {
          set((state) => {
            const session = state.currentTabKey ? state.tabSessions[state.currentTabKey] : null;
            if (session) {
              const message = session.messages.find((msg) => msg.id === id);
              if (message) {
                message.content = content;
              }
            }
          });
        },
        sendLoadingMessage: () => {
          get().sendMessage({ owner: 'bot', content: '', type: 'loading' });
          set((state) => {
            const session = state.currentTabKey ? state.tabSessions[state.currentTabKey] : null;
            if (session) {
              session.isWaiting = true;
            }
          });
        },
        modifyWaitingState: (isWaiting: boolean) => {
          set((state) => {
            const session = state.currentTabKey ? state.tabSessions[state.currentTabKey] : null;
            if (session) {
              session.isWaiting = isWaiting;
            }
          });
        },
        removeLoadingMessage: () => {
          set((state) => {
            const session = state.currentTabKey ? state.tabSessions[state.currentTabKey] : null;
            if (session && session.messages.length > 0) {
              session.messages.pop();
            }
          });
        },

        sendMessageForTab: (tabKey, message) => {
          set((state) => {
            const session = state.tabSessions[tabKey];
            if (session) {
              const newMessage = {
                ...message,
                id: message.id || uuidv4(),
                createdAt: message['createdAt'] || new Date().toISOString(),
              };
              session.messages.push(newMessage);
            }
          });
        },
        updateMessageForTab: (tabKey, { id, content }) => {
          set((state) => {
            const session = state.tabSessions[tabKey];
            if (session) {
              const message = session.messages.find((msg) => msg.id === id);
              if (message) {
                message.content = content;
              }
            }
          });
        },
        sendLoadingMessageForTab: (tabKey) => {
          set((state) => {
            const session = state.tabSessions[tabKey];
            if (session) {
              session.messages.push({
                id: uuidv4(),
                owner: 'bot',
                content: '',
                type: 'loading',
                createdAt: new Date().toISOString(),
              });
              session.isWaiting = true;
            }
          });
        },
        removeLoadingMessageForTab: (tabKey) => {
          set((state) => {
            const session = state.tabSessions[tabKey];
            if (session && session.messages.length > 0) {
              const last = session.messages[session.messages.length - 1];
              if (last.owner === 'bot' && last.type === 'loading') {
                session.messages.pop();
              }
            }
          });
        },
        modifyWaitingStateForTab: (tabKey, isWaiting) => {
          set((state) => {
            const session = state.tabSessions[tabKey];
            if (session) {
              session.isWaiting = isWaiting;
            }
          });
        },
        markTabAsInitialized: (tabKey) => {
          set((state) => {
            const session = state.tabSessions[tabKey];
            if (session) {
              session.hasInitializedCustomer = true;
            }
          });
        },

        // Decide if customer tab initialization should be implemented
        initializeCustomerTabIfNeeded: (tabKey: TabSessionKey) => {
          let shouldSend = false;

          set((state) => {
            const session = state.tabSessions[tabKey];
            if (session && !session.hasInitializedCustomer && session.messages.length === 0) {
              session.hasInitializedCustomer = true;
              shouldSend = true;
            }
          });

          return shouldSend;
        },
      }),
      // Define instance and name for devtools
      { name: 'chatbotStore', store: 'chatbotStore' },
    ),
  ),
);

export const useChatbotStore = createSelectors(useChatbotStoreBase);
export const useGetMessageIds = () =>
  useChatbotStoreBase(useShallow((state) => state.getMessages().map((msg) => msg.id)));
export const useGetMessageById = (id: string) =>
  useChatbotStoreBase(useShallow((state) => state.getMessages().find((msg) => msg.id === id)));

// Current tab selector
export const useCurrentTabSession = () => useChatbotStoreBase(useShallow((state) => state.getCurrentTabSession()));
export const useCurrentTabMessages = () => useChatbotStoreBase(useShallow((state) => state.getMessages()));
export const useCurrentTabIsWaiting = () => useChatbotStoreBase(useShallow((state) => state.getIsWaiting()));
export const useCurrentTabChatbotUrl = () => useChatbotStoreBase(useShallow((state) => state.getChatbotUrl()));
export const useCurrentTabMessageHasStarted = () =>
  useChatbotStoreBase(useShallow((state) => state.getMessageHasStarted()));
