import { StoreApi, UseBoundStore } from 'zustand';

type WithSelectors<S> = S extends { getState: () => infer T } ? S & { use: { [K in keyof T]: () => T[K] } } : never;

export const createSelectors = <S extends UseBoundStore<StoreApi<object>>>(_store: S) => {
  const store = _store as WithSelectors<typeof _store>;
  store.use = {};
  for (const k of Object.keys(store.getState())) {
    (store.use as any)[k] = () => store((s) => s[k as keyof typeof s]);
  }

  return store;
};

export type BotMessageFromWs = {
  response_type: string;
  completion_id: string;
  delta: string;
  status: string;
  error: boolean;
  message_id: string;
  trace_id: string;
  timestamp: string;
};

export type UserMessageToSend = {
  owner: 'user';
  content: string;
};

export type BotMessageToSend = {
  owner: 'bot';
  content: string;
  type?: 'loading' | 'text';
};

export type BaseMessage = {
  id?: string;
  createdAt?: string;
};

export type ChatMessageToSend = (UserMessageToSend | BotMessageToSend) & BaseMessage;
export type ChatMessageEntity = { id: string } & ChatMessageToSend;

export type TabSessionKey = string; // Format: "tabId-tabType" e.g., "123456-customer" or "123456-device"

export type ChatbotTabSession = {
  messages: Array<ChatMessageEntity>;
  messageHasStarted: boolean;
  isWaiting: boolean;
  chatbotUrl: string;
  isInitialized: boolean;
  isOpen: boolean;
  hasInitializedCustomer: boolean;
};

export type ChatbotTabSessions = Record<TabSessionKey, ChatbotTabSession>;
