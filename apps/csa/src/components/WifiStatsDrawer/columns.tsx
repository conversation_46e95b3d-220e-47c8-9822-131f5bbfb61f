import { useTranslation } from 'react-i18next';
import { StatusTrend } from '@/components/StatusTrend';

export const useColumn = () => {
  const { t } = useTranslation();

  const columns = [
    { accessorKey: 'type', header: t('device:deviceInfo.cpeStats.table.header.type'), cell: (info) => info.getValue() },
    {
      accessorKey: 'minMax',
      header: t('device:deviceInfo.cpeStats.table.header.minMax'),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: 'average',
      header: t('device:deviceInfo.cpeStats.table.header.average'),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: 'latestResult',
      header: t('device:deviceInfo.cpeStats.table.header.latestResult'),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: 'noOfTest',
      header: t('device:deviceInfo.cpeStats.table.header.noOfTests'),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: 'weeklyTrends',
      header: t('device:deviceInfo.cpeStats.table.header.weeklyTrend'),
      cell: ({ row }) => <StatusTrend data={row.original.weeklyTrends || []} />,
    },
  ];

  return columns;
};
