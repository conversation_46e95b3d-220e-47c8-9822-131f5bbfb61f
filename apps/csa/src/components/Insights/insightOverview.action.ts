import useHealthCheckStore from '@/components/HealthCheck/store';
import { HealthCheckStore, Slice } from '@/components/HealthCheck/types';
import { useGetSelectedRequestInfo } from '@/stores/realtime.store';
import { useTabDeviceId, useTabLineId, useTabType } from '@/stores/tab.store';
import get from 'lodash/get';
import { useMemo } from 'react';
import { useGetRealtimeInsight } from 'services/Realtime';
import { getUnixTime } from 'services/Utils';
import { useShallow } from 'zustand/react/shallow';
import { IInsight, wheelTypeMap, InsightReponse } from './insight.type';
import { conditionFilteringForClient, sortDataInsight } from './insight.utils';
import { ALL_VALUE_OPTION } from './InsightsDrawer';
import { useIsUseHistoricalData } from '@/hooks/useIsUseHistoricalData';
import { useGetData, EDataSection } from 'services/GetData';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';

export const useInsightOverviewAction = () => {
  const tabType = useTabType();
  const lineId = useTabLineId() || '';
  const deviceId = useTabDeviceId() || '';
  const selectedRequest = useGetSelectedRequestInfo(lineId, 'LINE');
  const isDeviceTab = tabType === 'device';

  const { selectedSlice } = useHealthCheckStore(
    useShallow((state: HealthCheckStore) => ({
      selectedSlice: state.selectedSlice,
    })),
  );
  // Suggested name: wheelFilterKey, since this variable represents the key used for filtering by wheel type
  const wheelFilterKey = useMemo(() => {
    if (isDeviceTab) return ALL_VALUE_OPTION;
    const key = selectedSlice !== null ? wheelTypeMap[selectedSlice] : ALL_VALUE_OPTION;
    return key.toLowerCase();
  }, [isDeviceTab, selectedSlice]);
  const realtimeRequestId = get(selectedRequest, 'realtimeRequestId') ?? '';

  const timeRangeSelected = useGetTimeRangeByLine(lineId);
  const startDate = timeRangeSelected?.startDate || null;
  const endDate = timeRangeSelected?.endDate || null;

  const isRealtimeDataReady = get(selectedRequest, 'isDataReady', false);
  const isUseHistoricalData = useIsUseHistoricalData(lineId, 'LINE');

  const isRealtimeRequesting = get(selectedRequest, 'isRealtimeRequesting', false);

  const {
    data: insightHistoricalRes,
    isLoading: isInsightLoading,
    ...rest
    // @ts-ignore: getData is a remote untyped generic, we ignore it at runtime since module federation can't infer the generic type
  } = useGetData<InsightReponse>(
    {
      data: EDataSection.INSIGHTS,
      lineId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: Boolean(lineId) && Boolean(startDate) && isUseHistoricalData && !isRealtimeDataReady,
      staleTime: Infinity,
    },
  );

  const { data: realtimeResponse, isFetching: isRealtimeInsightLoading } = useGetRealtimeInsight(
    {
      lineId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
      realtimeRequestId,
    },
    {
      enabled:
        isRealtimeDataReady &&
        Boolean(startDate) &&
        Boolean(lineId) &&
        Boolean(realtimeRequestId) &&
        !isUseHistoricalData,
      staleTime: Infinity,
    },
  );

  const isRequestingRealtime = isRealtimeRequesting || isRealtimeInsightLoading;
  const insightsData = isUseHistoricalData ? insightHistoricalRes : realtimeResponse;

  const insights = useMemo(() => {
    const data: IInsight[] = get(insightsData, 'data.insights') || [];
    return sortDataInsight(data);
  }, [insightsData]);

  const filteredDataOverview = useMemo(() => {
    const result = insights.filter((item) => {
      if (isDeviceTab) {
        return item.relatedDeviceIds?.includes(deviceId);
      }
      const condition =
        wheelFilterKey === ALL_VALUE_OPTION || wheelFilterKey === item.advanced?.wheelType.toLowerCase();
      // wheel clients also include extender, so we need to get all of insight of that extender.
      if (selectedSlice === Slice.CLIENTS) {
        return wheelFilterKey === ALL_VALUE_OPTION || conditionFilteringForClient(item, lineId);
      }

      // Currently, since we only support Internet service, this wheel shows exact same data that we show in WAN wheel
      if (selectedSlice === Slice.SERVICES) {
        return (
          wheelFilterKey === ALL_VALUE_OPTION ||
          item.advanced?.wheelType.toLowerCase() === wheelTypeMap[0].toLowerCase()
        );
      }
      return condition;
    });
    return sortDataInsight(result);
  }, [insights, wheelFilterKey, isDeviceTab, deviceId, lineId, selectedSlice]);

  return {
    ...rest,
    isLoading: isInsightLoading || isRequestingRealtime,
    filteredDataOverview,
  };
};
