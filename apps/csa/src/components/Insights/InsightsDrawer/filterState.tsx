import { useTabDeviceId, useTabLineId, useTabType } from '@/stores/tab.store';
import { useDrawerStore, useInsightDrawer } from '@/stores/drawer.store';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';

export function useFilterState() {
  const lineId = useTabLineId();
  const timeRangeSelected = useGetTimeRangeByLine(lineId || '');
  const defaultStartDate = timeRangeSelected?.endDate || null;
  const defaultEndDate = timeRangeSelected?.endDate || null;

  const deviceId = useTabDeviceId();
  const tabType = useTabType();

  const updateDrawer = useDrawerStore(useShallow((state) => state.updateDrawerInsight));

  const { searchText, startDate, endDate, selectedDeviceId, category, severity, sortTime, type, open, stationMac } =
    useInsightDrawer();

  useEffect(() => {
    if (!open) {
      updateDrawer({
        searchText: '',
        startDate: defaultStartDate,
        endDate: defaultEndDate,
        selectedDeviceId: tabType === 'device' ? deviceId || '' : '',
        severity: '',
        sortTime: '',
        type: '',
        stationMac: '',
      });
    }
  }, [open, defaultEndDate, defaultStartDate, updateDrawer, tabType, deviceId]);

  const changeFilter = ({
    startDate,
    endDate,
    searchText,
    selectedDeviceId,
    category,
    severity,
    sortTime,
    type,
    stationMac,
  }: {
    startDate?: Date | null;
    endDate?: Date | null;
    searchText?: string;
    selectedDeviceId?: string;
    category?: string;
    severity?: string;
    sortTime?: string;
    type?: string;
    stationMac?: string;
  }) => {
    updateDrawer({
      ...(startDate !== undefined ? { startDate } : {}),
      ...(endDate !== undefined ? { endDate } : {}),
      ...(searchText !== undefined ? { searchText } : {}),
      ...(selectedDeviceId !== undefined ? { selectedDeviceId } : {}),
      ...(category !== undefined ? { category } : {}),
      ...(severity !== undefined ? { severity } : {}),
      ...(sortTime !== undefined ? { sortTime } : {}),
      ...(type !== undefined ? { type } : {}),
      ...(stationMac !== undefined ? { stationMac } : {}),
    });
  };

  return {
    selectedDeviceId,
    searchText,
    startDate,
    endDate,
    deviceId,
    category,
    severity,
    sortTime,
    type,
    changeFilter,
    stationMac,
  };
}
