import { useTabDeviceId, useTabLineId } from '@/stores/tab.store';
import { zodResolver } from '@hookform/resolvers/zod';
import { AxiosResponse } from 'axios';
import get from 'lodash/get';
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { usePingTest } from 'services/Actions';
import { useGetLineInfo } from 'services/LineInfo';
import { IconIllustrationSuccess, LinearDangerTriangle, Loading } from 'ui/UIAssets';
import {
  AxonAlertDialog,
  AxonAlertDialogAction,
  AxonAlertDialogCancel,
  AxonAlertDialogContent,
  AxonAlertDialogDescription,
  AxonAlertDialogFooter,
  AxonAlertDialogHeader,
  AxonAlertDialogTitle,
  AxonDialog,
  AxonDialogContent,
  AxonDialogDescription,
  AxonDialogHeader,
  AxonDialogTitle,
  AxonInput,
  AxonLabel,
  AxonButton,
} from 'ui/UIComponents';
import { z } from 'zod';
import { PingTestDTO } from '../../ClientsDrawer.type';

type Props = {
  open: boolean;
  onClose: () => void;
  macAddress?: string;
  ipAddress?: string;
  clientName?: string;
};

const MIN_PING_REQUESTS = 1;
const MAX_PING_REQUESTS = 15;

const PingTestDialog = (props: Props) => {
  const { onClose, open, macAddress, ipAddress, clientName } = props;
  const { t } = useTranslation();

  const pingTestSchema = z.object({
    numOfPingRequests: z
      .number({ invalid_type_error: t('device:deviceInfo.action.pingTest.dialog.numOfPingRequestsWarning') })
      .int(t('device:deviceInfo.action.pingTest.dialog.numOfPingRequestsWarning'))
      .min(MIN_PING_REQUESTS, t('device:deviceInfo.action.pingTest.dialog.numOfPingRequestsWarning'))
      .max(MAX_PING_REQUESTS, t('device:deviceInfo.action.pingTest.dialog.numOfPingRequestsWarning'))
      .default(MIN_PING_REQUESTS),
    macAddress: z.string().default(''),
    ipAddress: z.string().default(''),
  });

  type PingTestForm = z.infer<typeof pingTestSchema>;
  const deviceId = useTabDeviceId();
  const localLineId = useTabLineId(); // the line is getting from store

  const { data: lineInfo } = useGetLineInfo(deviceId || localLineId || '');
  const lineId = get(lineInfo, 'data.id');

  const [openResultDialog, setOpenResultDialog] = useState(false);

  const { data, error, mutate: triggerPingTest, isPending } = usePingTest();
  const pingTestData = get(data, 'data');
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<PingTestForm>({
    defaultValues: {
      numOfPingRequests: 1,
      macAddress: '',
      ipAddress: '',
    },
    resolver: zodResolver(pingTestSchema as any),
    mode: 'onChange',
  });

  // Control dialog open state manually
  const [dialogOpen, setDialogOpen] = useState(open);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update our internal state when props change
  useEffect(() => {
    setDialogOpen(open);
  }, [open]);

  function handleAction(formData: PingTestForm) {
    if (!lineId) return;

    // Set flag to prevent dialog from closing
    setIsSubmitting(true);

    triggerPingTest(
      {
        numOfPingRequests: formData.numOfPingRequests,
        lineId,
        macAddress: macAddress ?? '',
        ipAddress: ipAddress ?? '',
      },
      {
        onSettled: () => {
          setOpenResultDialog(true);
          // Reset flag after operation completes
          setIsSubmitting(false);
        },
      },
    );
  }

  function handleDialogOpenChange(isOpen: boolean) {
    // Only allow closing if we're not submitting
    if (!isOpen && !isSubmitting && !isPending) {
      setDialogOpen(false);
      reset();
      onClose();
    } else {
      // Keep dialog open if we're submitting
      setDialogOpen(true);
    }
  }

  const onCancel = () => {
    if (!isSubmitting && !isPending) {
      setDialogOpen(false);
      reset();
      onClose();
    }
  };

  return (
    <>
      <AxonAlertDialog open={dialogOpen} onOpenChange={handleDialogOpenChange}>
        <AxonAlertDialogContent className='min-w-[350px] gap-0' data-testid='ping-test-dialog-content'>
          <AxonAlertDialogHeader className='gap-xl items-start text-left'>
            <AxonAlertDialogTitle>{t('device:deviceInfo.action.pingTest.dialog.title')}</AxonAlertDialogTitle>
            <AxonAlertDialogDescription className='text-content-secondary text-md'>
              {t('device:deviceInfo.action.pingTest.dialog.description')}
            </AxonAlertDialogDescription>
          </AxonAlertDialogHeader>
          <form onSubmit={handleSubmit(handleAction)} className='gap-xl my-6 flex flex-col' id='ping-test-form'>
            <div className='gap-xs flex flex-1 flex-col'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='clientName'>
                {t('device:deviceInfo.action.pingTest.dialog.clientName')}
              </AxonLabel>
              <AxonInput
                disabled
                id='clientName'
                type='text'
                value={clientName}
                data-testid='ping-test-client-name-input'
              />
            </div>
            <div className='gap-xs flex flex-1 flex-col'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='ipAddress'>
                {t('device:deviceInfo.action.pingTest.dialog.ipAddress')}
              </AxonLabel>
              <AxonInput
                disabled
                id='ipAddress'
                type='text'
                value={ipAddress}
                data-testid='ping-test-ip-address-input'
              />
            </div>
            <div className='gap-xs flex flex-1 flex-col'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='numOfPingRequests'>
                {t('device:deviceInfo.action.pingTest.dialog.numOfPingRequests')}
              </AxonLabel>
              <AxonInput
                disabled={(!macAddress && !ipAddress) || isPending}
                id='numOfPingRequests'
                type='number'
                min={MIN_PING_REQUESTS}
                max={MAX_PING_REQUESTS}
                step={1}
                autoFocus
                aria-invalid={!!errors.numOfPingRequests}
                data-testid='ping-test-requests-input'
                onKeyDown={(e) => {
                  if (e.key === '.' || e.key === ',' || e.key === 'e') {
                    e.preventDefault();
                  }
                }}
                defaultValue={MIN_PING_REQUESTS}
                {...register('numOfPingRequests', {
                  valueAsNumber: true,
                })}
              />
              {errors.numOfPingRequests && <p className='text-error-500 text-sm'>{errors.numOfPingRequests.message}</p>}
            </div>
          </form>
          <AxonAlertDialogFooter className='flex-nowrap gap-3'>
            <AxonAlertDialogCancel
              onClick={onCancel}
              className='basis-1/2'
              disabled={isSubmitting || isPending}
              data-testid='ping-test-cancel-button'>
              {t('device:deviceInfo.action.cancelText')}
            </AxonAlertDialogCancel>
            <AxonButton
              type='submit'
              form='ping-test-form'
              variant='primary'
              className='basis-1/2'
              disabled={isSubmitting || isPending}
              data-testid='ping-test-run-button'>
              {(isSubmitting || isPending) && <Loading className='size-4 animate-spin' />}
              {isSubmitting || isPending
                ? t('device:deviceInfo.action.testing')
                : t('device:deviceInfo.action.confirmText')}
            </AxonButton>
          </AxonAlertDialogFooter>
        </AxonAlertDialogContent>
      </AxonAlertDialog>
      {openResultDialog && (
        <PingTestResultDialog data={pingTestData} error={error} open onClose={() => setOpenResultDialog(false)} />
      )}
    </>
  );
};

export default PingTestDialog;

type PingTestResultDialogProps = {
  open: boolean;
  onClose: () => void;
  data: PingTestDTO | null;
  error: AxiosResponse<{
    code: number;
    message: string;
    data: null;
  }> | null;
};

const PingTestResultDialog = (props: PingTestResultDialogProps) => {
  const { open, onClose, data, error } = props;
  const { t } = useTranslation();

  const [viewLog, setViewLog] = useState(false);

  const isSuccess = Boolean(data);
  const isError = Boolean(error);

  const Icon = isSuccess ? (
    <IconIllustrationSuccess className='text-content-meta-green h-[52px] w-[58px]' />
  ) : (
    <LinearDangerTriangle className='text-content-meta-red h-[52px] w-[58px]' />
  );
  const title = isSuccess
    ? t('device:deviceInfo.action.pingTest.dialog.result.title.success')
    : t('device:deviceInfo.action.pingTest.dialog.result.title.failToStartTest');

  const packageSent = get(data, 'numberOfPacketsSent', 0);
  const packageLost = get(data, 'lostPacketsCount', 0);
  const packageReceived = packageSent - packageLost;
  const packageLostPercentage = get(data, 'lostPacketsPercentage', 0);
  const packageMin = get(data, 'minimumMs', 0);
  const packageMax = get(data, 'maximumMs', 0);
  const packageAvg = get(data, 'averageMs', 0);

  return (
    <>
      <AxonAlertDialog open={open} onOpenChange={onClose}>
        <AxonAlertDialogContent aria-describedby={undefined} className='gap-xs w-xs max-w-full text-center'>
          <AxonAlertDialogHeader className='gap-xl'>
            {Icon}
            <AxonAlertDialogTitle className='text-content-primary'>{title}</AxonAlertDialogTitle>
          </AxonAlertDialogHeader>
          {isSuccess ? (
            <AxonAlertDialogDescription className='gap-md text-md text-content-secondary flex flex-col font-normal leading-[150%]'>
              <div>
                <p>{t('device:deviceInfo.action.pingTest.dialog.packets')}</p>
                <p>
                  {t('device:deviceInfo.action.pingTest.dialog.sent')}: {packageSent},{' '}
                  {t('device:deviceInfo.action.pingTest.dialog.received')}: {packageReceived},{' '}
                  {t('device:deviceInfo.action.pingTest.dialog.lost')}: {`${packageLostPercentage}%`}
                </p>
              </div>
              <div>
                <p>{t('device:deviceInfo.action.pingTest.dialog.approximateRoundTripTime')}</p>
                <p>
                  {t('device:deviceInfo.action.pingTest.dialog.min')}: {packageMin}ms,{' '}
                  {t('device:deviceInfo.action.pingTest.dialog.max')}: {packageMax}ms,{' '}
                  {t('device:deviceInfo.action.pingTest.dialog.avg')}: {packageAvg}ms
                </p>
              </div>
              <div>
                <p>
                  {t('device:deviceInfo.action.pingTest.dialog.numOfPingRequests')}: {data?.numberOfPacketsSent}
                </p>
              </div>
            </AxonAlertDialogDescription>
          ) : (
            <AxonAlertDialogDescription className='text-md text-content-secondary font-normal leading-[150%]'>
              {get(
                error,
                'data.message',
                t('device:deviceInfo.action.message.pingTest.defaultError', { errorCode: error?.data?.code }),
              )}
            </AxonAlertDialogDescription>
          )}
          <AxonAlertDialogFooter className='pt-xl flex justify-center gap-3'>
            <AxonAlertDialogCancel
              onClick={onClose}
              className='text-content-primary font-book leading-[120%]'
              data-testid='ping-test-result-close-button'>
              {t('device:deviceInfo.action.close')}
            </AxonAlertDialogCancel>
            {isSuccess && (
              <AxonAlertDialogAction
                onClick={() => setViewLog(true)}
                variant='primary'
                data-testid='ping-test-result-view-log-button'
                disabled={isError}
                className='font-book leading-[120%] text-white'>
                {t('device:deviceInfo.action.viewLog')}
              </AxonAlertDialogAction>
            )}
          </AxonAlertDialogFooter>
        </AxonAlertDialogContent>
      </AxonAlertDialog>
      {viewLog && data && <PingTestLogDialog open onClose={() => setViewLog(false)} data={data} />}
    </>
  );
};

const PingTestLogDialog = (props: { open: boolean; onClose: () => void; data: PingTestDTO }) => {
  const { open, onClose, data } = props;

  const { t } = useTranslation();

  return (
    <AxonDialog open={open} onOpenChange={onClose}>
      <AxonDialogContent
        aria-describedby={undefined}
        className='gap-xs w-sm'
        overlayClassName='bg-transparent'
        data-testid='ping-test-log-dialog-content'>
        <AxonDialogHeader>
          <AxonDialogTitle>{t('device:deviceInfo.action.viewLog')}</AxonDialogTitle>
        </AxonDialogHeader>
        <AxonDialogDescription className='h-[480px] overflow-y-auto'>
          <code
            className='text-content-secondary whitespace-pre-wrap text-left'
            data-testid='ping-test-log-dialog-code'>
            {JSON.stringify(data, null, 2)}
          </code>
        </AxonDialogDescription>
      </AxonDialogContent>
    </AxonDialog>
  );
};
