import { useTabDeviceId, useTabLineId, useTabType } from '@/stores/tab.store';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';
import get from 'lodash/get';
import { useMemo } from 'react';
import { useGetData, EDataSection } from 'services/GetData';
import { getUnixTime } from 'services/Utils';
import { ClientConnectionResponse } from './ClientsTable/type';

export const useClientsDrawerAction = () => {
  const tabType = useTabType();
  const lineId = useTabLineId();
  const deviceId = useTabDeviceId();
  const isDeviceTab = tabType === 'device';
  const timeRangeSelected = useGetTimeRangeByLine(lineId || '');
  const startDate = get(timeRangeSelected, 'startDate', null);
  const endDate = get(timeRangeSelected, 'endDate', null);

  // @ts-ignore: ignore runtime error with infer generic type
  const dataQueries = useGetData<ClientConnectionResponse>(
    {
      lineId: lineId || '',
      // TODO: when using device id the data usage is always null, need to check in the BFF
      // deviceId: deviceId || '',
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
      data: EDataSection.CLIENT_CONNECTION,
    },
    {
      enabled: isDeviceTab ? !!deviceId : !!lineId,
      staleTime: Infinity,
    },
  );

  const { data: healthCheckClientExtended } = dataQueries;

  const clientStatsOptions = useMemo(
    () =>
      healthCheckClientExtended?.data?.results?.map((item) => ({
        label: item.deviceInfo.deviceName,
        value: item.networkAddress.macAddress,
        cpeId: item.cpeId,
      })) || [],
    [healthCheckClientExtended],
  );

  return {
    ...dataQueries,
    clientStatsOptions,
  };
};
