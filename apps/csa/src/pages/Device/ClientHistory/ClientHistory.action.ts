import useTabStore, { useConfigWidgetClientHistory, useTabDeviceId } from '@/stores/tab.store';
import { useGetCpeInfo } from 'services/CPEService';
import { getDayjsFromUnixTime, getUnixTime, getTimezone, getDayjsFromDate } from 'services/Utils';
import { useGetClientHistory } from 'services/Client';
import get from 'lodash/get';
import { useMemo } from 'react';
import { ClientHistoryResponse, CpeClient } from './type';
import isEmpty from 'lodash/isEmpty';
import { ClientHistoryEMetric } from '@/stores/widgets.config';
import uniqBy from 'lodash/uniqBy';
import { ClientHistoryLegend, IPayload } from './Legend';
import { ClientHistoryTooltip } from './Tooltip';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';

export const ETHERNET_METRICS = [
  ClientHistoryEMetric.TRAFFIC_DOWN,
  ClientHistoryEMetric.TRAFFIC_UP,
  ClientHistoryEMetric.QOE,
];

const lineChartConfig = {
  [ClientHistoryEMetric.WIFI_THROUGHPUT]: {
    yAxisProps: [
      {
        yAxisId: ClientHistoryEMetric.WIFI_THROUGHPUT,
        label: {
          value: 'Mbps',
          dx: -20,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.LATENCY]: {
    yAxisProps: [
      {
        yAxisId: ClientHistoryEMetric.LATENCY,
        label: {
          value: 'ms',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.RSSI]: {
    yAxisProps: [
      {
        yAxisId: ClientHistoryEMetric.RSSI,
        domain: [-100, 0],
        label: {
          value: 'dBm',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.SNR]: {
    yAxisProps: [
      {
        yAxisId: ClientHistoryEMetric.SNR,
        domain: [0, 40],
        label: {
          value: 'dB',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.TRAFFIC_DOWN]: {
    yAxisProps: [
      {
        yAxisId: ClientHistoryEMetric.TRAFFIC_DOWN,
        label: {
          value: 'MB',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.TRAFFIC_UP]: {
    yAxisProps: [
      {
        yAxisId: ClientHistoryEMetric.TRAFFIC_UP,
        label: {
          value: 'MB',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.WIFI_PHY_RATE]: {
    yAxisProps: [
      {
        yAxisId: ClientHistoryEMetric.WIFI_PHY_RATE,
        label: {
          value: 'Mbps',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.QOE]: {
    yAxisProps: [
      {
        yAxisId: ClientHistoryEMetric.QOE,
        label: {
          value: 'QoE',
          dx: -50,
          angle: -90,
        },
      },
    ],
  },
};

function getClientName(client: CpeClient, parentId: string) {
  const name = client.name ? `${client.name} (${client.mac})` : client.mac;
  return `${name} (${parentId})`;
}

function generateConfig(
  metric: ClientHistoryEMetric,
  listOfKeys: { key: string; name: string; lineDash?: string; color?: string; uniqueName?: boolean }[],
) {
  const result = {
    linesProps: listOfKeys.map(({ key, name, lineDash, color }, i) => {
      return {
        yAxisId: metric,
        dataKey: key,
        color: color || `rgb(var(--chart-${(i % 30) + 1}))`, // max 30 variables colors predefined in global.css
        name: name,
        connectNulls: metric === ClientHistoryEMetric.QOE,
        strokeDasharray: lineDash,
        ...(metric === ClientHistoryEMetric.QOE && { type: 'stepAfter' as const }),
      };
    }),
    yAxisProps: get(lineChartConfig[metric], 'yAxisProps'),
    legendProps: {},
    tooltipProps: {},
  };

  if (metric === ClientHistoryEMetric.WIFI_PHY_RATE) {
    const payload: IPayload[] = [];
    const uniquePayload = uniqBy(listOfKeys, 'name');
    uniquePayload.forEach(({ name, color }, i) => {
      payload.push({
        id: name,
        value: name,
        color: color || `rgb(var(--chart-${(i % 30) + 1}))`, // max 30 variables colors predefined in global.css
      });
    });
    payload.push({
      id: 'line-solid',
      value: 'Rx',
      type: 'solid',
      color: 'rgb(var(--error-500))',
    });
    payload.push({
      id: 'line-dashed',
      value: 'Tx',
      type: 'dashed',
      color: 'rgb(var(--error-500))',
    });
    result.legendProps = {
      payload,
      content: ClientHistoryLegend,
    };
    result.tooltipProps = {
      content: ClientHistoryTooltip,
    };
  }

  return result;
}

export const useClientHistoryAction = () => {
  const deviceId = useTabDeviceId() || '';

  const { data: cpeInfo, isError: isCpeInfoError } = useGetCpeInfo(deviceId, {
    enabled: !!deviceId,
    staleTime: Infinity,
  });

  const lineId = get(cpeInfo, 'data.lineId') ?? '';
  const timeRangeSelected = useGetTimeRangeByLine(lineId);

  // TODO: temporary fix, will be remove when we have a dedicated store to make the graph timerange
  const startDate = timeRangeSelected?.endDate
    ? getDayjsFromDate(timeRangeSelected?.endDate).subtract(2, 'day').toDate()
    : null;
  const endDate = timeRangeSelected?.endDate || null;
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const clientHistoryConfig = useConfigWidgetClientHistory()!;
  const { filterState } = clientHistoryConfig;

  const {
    data,
    isError: isCpeHistoryError,
    ...rest
  } = useGetClientHistory(
    {
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: !!lineId && !!deviceId && !!startDate,
    },
  );

  const chartData = useMemo(() => {
    const { client: clientFiltered, metric, band: bandFiltered, cpeIds: cpeIdsFiltered } = filterState;
    const results = get(data, 'data.results') as ClientHistoryResponse['results'];
    const formatData: Map<number, Record<string, number>> = new Map();
    const listOfKeys = {};

    if (
      results &&
      cpeIdsFiltered &&
      cpeIdsFiltered.length > 0 &&
      clientFiltered &&
      clientFiltered.length > 0 &&
      bandFiltered &&
      bandFiltered.length > 0
    ) {
      Object.entries(results).forEach(([cpeId, datapoints]) => {
        const availableDataForKey = new Map<string, boolean>(); // if a key is true, then its data is available
        if (cpeIdsFiltered.includes(cpeId)) {
          datapoints.forEach((point) => {
            const { date, stations } = point;
            const listDataForClients = {};
            clientFiltered.forEach((client) => {
              bandFiltered.forEach((band) => {
                const bandId = band.bandId;

                const addToLegend = (key: string, value: number, extraProps = {}) => {
                  if (!availableDataForKey.has(key)) return;

                  listDataForClients[key] = value;
                  if (!listOfKeys[key]) {
                    listOfKeys[key] = {
                      key,
                      name: `${band.label}: ${getClientName(client, cpeId)}`,
                      ...extraProps,
                    };
                  }
                };

                if (metric === ClientHistoryEMetric.WIFI_PHY_RATE) {
                  const txKey = `${client.mac}.${bandId}.txPhyRate`;
                  const rxKey = `${client.mac}.${bandId}.rxPhyRate`;
                  const txValue = get(stations, txKey, null) as unknown as number;
                  const rxValue = get(stations, rxKey, null) as unknown as number;
                  const txLegendKey = `${cpeId}.${txKey}`;
                  const rxLegendKey = `${cpeId}.${rxKey}`;
                  if (txValue !== null && !availableDataForKey.has(txLegendKey)) {
                    availableDataForKey.set(txLegendKey, true);
                  }
                  if (rxValue !== null && !availableDataForKey.has(rxLegendKey)) {
                    availableDataForKey.set(rxLegendKey, true);
                  }
                  const indexOfKey = Object.keys(listOfKeys).length;
                  const phyRateProps = {
                    isPhyRate: true,
                    color: `rgb(var(--chart-${(indexOfKey % 30) + 1}))`,
                  };
                  addToLegend(`${cpeId}.${txKey}`, txValue, { phyRateProps, lineDash: '5, 5' });
                  addToLegend(`${cpeId}.${rxKey}`, rxValue, phyRateProps);
                } else if (metric === ClientHistoryEMetric.RSSI) {
                  const rssiAvgKey = `${client.mac}.${bandId}.rssiAvg`;
                  const rssiAvgValue = get(stations, rssiAvgKey, null) as unknown as number;
                  const rssiAvgLegendKey = `${cpeId}.${rssiAvgKey}`;
                  if (rssiAvgValue !== null && !availableDataForKey.has(rssiAvgLegendKey)) {
                    availableDataForKey.set(rssiAvgLegendKey, true);
                  }

                  if (clientFiltered.length === 1) {
                    const rssiMinKey = `${client.mac}.${bandId}.rssiMin`;
                    const rssiMinValue = get(stations, rssiMinKey, null) as unknown as number;
                    const rssiMinLegendKey = `${cpeId}.${rssiMinKey}`;
                    if (rssiMinValue !== null && !availableDataForKey.has(rssiMinLegendKey)) {
                      availableDataForKey.set(rssiMinLegendKey, true);
                    }
                    addToLegend(rssiMinLegendKey, rssiMinValue, {
                      name: `${band.label}: RSSI Min ${client.name} (${cpeId})`,
                    });
                  }
                  const rssiProps: { name?: string } = {};
                  if (clientFiltered.length === 1) {
                    rssiProps.name = `${band.label}: RSSI Avg ${client.name} (${cpeId})`;
                  }
                  addToLegend(rssiAvgLegendKey, rssiAvgValue, rssiProps);
                } else {
                  const key =
                    metric === ClientHistoryEMetric.QOE ? `${client.mac}.qoe` : `${client.mac}.${bandId}.${metric}`;
                  const value = get(stations, key, null) as unknown as number;
                  const legendKey = `${cpeId}.${key}`;
                  if (value !== null && !availableDataForKey.has(legendKey)) {
                    availableDataForKey.set(legendKey, true);
                  }
                  addToLegend(legendKey, value);
                }
              });
            });
            if (!isEmpty(listDataForClients)) {
              // Skip QOE data unless it's start of day
              if (metric === ClientHistoryEMetric.QOE) {
                const day = getDayjsFromUnixTime(date)?.tz(getTimezone());
                const isStartOfDay = day?.get('hour') === 0;
                if (!isStartOfDay) return;
              }

              // Add or merge data for this date
              const existingData = formatData.get(date) || { date };
              formatData.set(date, { ...existingData, ...listDataForClients });
            }
          });
        }
      });
    }
    return {
      config: generateConfig(metric, Object.values(listOfKeys)),
      data: Array.from(formatData.values()),
    };
  }, [filterState, data]);
  const clientList = useMemo<CpeClient[]>(() => {
    const clientData = get(data, 'data.clients') ?? [];
    const bandData = get(data, 'data.bands') ?? [];
    const defaultBand = bandData.filter((band) => band.connectionType === 'wifi');

    addConfigEnhance('widgets.clientHistory.filterState.client', clientData);
    addConfigEnhance('widgets.clientHistory.filterState.band', defaultBand);
    addConfigEnhance('widgets.clientHistory.filterState.cpeIds', [deviceId]);
    return clientData;
  }, [data, addConfigEnhance, deviceId]);

  const bands = useMemo(() => {
    const bands = get(data, 'data.bands') ?? [];
    return [...bands].sort((a, b) => a.label.localeCompare(b.label)); // sort by alphabet
  }, [data]);

  const cpeList = useMemo(() => {
    return get(data, 'data.cpeIds') || [];
  }, [data]);

  return {
    ...rest,
    isError: isCpeInfoError || isCpeHistoryError,
    data,
    filter: filterState,
    setFilter: addConfigEnhance,
    chartData,
    clientList,
    bands,
    cpeList,
  };
};
